#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蛋白组COX回归分析 - 非心血管死亡相关分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import seaborn as sns
from lifelines import CoxPHFitter
from statsmodels.stats.multitest import multipletests
import warnings
warnings.filterwarnings('ignore')

# 设置字体和样式
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('default')

def load_and_prepare_data():
    """加载和准备数据"""
    print("正在加载数据...")
    
    # 读取合并后的数据
    df = pd.read_csv('merged_protein_clinical_data_new.csv', low_memory=False)
    
    print(f"原始数据形状: {df.shape}")
    
    # 去除重复的Record_ID，保留第一个
    df_unique = df.drop_duplicates(subset=['Record_ID'], keep='first')
    print(f"去重后数据形状: {df_unique.shape}")
    
    # 检查关键变量
    print(f"NonCV_death缺失值: {df_unique['NonCV_death'].isnull().sum()}")
    print(f"OS缺失值: {df_unique['OS'].isnull().sum()}")
    print(f"HFsnEF缺失值: {df_unique['HFsnEF'].isnull().sum()}")
    
    # 检查NonCV_death的分布
    print(f"NonCV_death分布:")
    print(df_unique['NonCV_death'].value_counts())
    
    return df_unique

def get_protein_columns(df):
    """获取蛋白列名"""
    # 假设蛋白列从某个位置开始，这里需要根据实际数据调整
    clinical_cols = ['Record_ID', 'HFsnEF', 'Plasma_sapmle', 'Sex', 'Age', 'Birthday', 
                    'Nationnality', 'IN_type', 'IN_DATE', 'OUT_DATE', 'IN_DAYS', 'Dyspnea', 
                    'Edema', 'Lung_rales', 'Fatigue', 'SBP ', 'DBP ', 'PR', 'Height', 'Weight', 
                    'BMI', 'BSA', 'HF_type_onset ', 'NYHA_class', 'CHD', 'PCI ', 'CABG', 
                    'Hypertension', 'Atrial_fibrillation', 'Atrial_flutter', 'HF_history', 
                    'HF_duration_month', 'Pacemaker', 'Heart_valve_surgery', 'Stroke', 'Diabetes', 
                    'Hyperlipedemia', 'Anemia', 'CKD', 'COPD', 'Asthma', 'Interstitial_pulmonary_fibrosis', 
                    'Malignancy', 'Thyroid_disease', 'Smoke', ' Alcoholic_drinking', 'Blood_type']
    
    # 找到所有不在临床变量列表中的列，并且不包含生存分析相关的列
    survival_cols = ['Alive', 'CV death', 'OS', 'HF_hospitalization', 'HF hospitalization', 
                    'Time to first HF hospitalization', 'Compostite endpoint of CV death or HF hospitalization',
                    'All_cause_hospitalization', 'NonCV_death', 'Time_to first_all_casue_hospitalization']
    
    exclude_cols = set(clinical_cols + survival_cols)
    
    protein_cols = [col for col in df.columns if col not in exclude_cols]
    
    print(f"识别到 {len(protein_cols)} 个蛋白列")
    print(f"前10个蛋白: {protein_cols[:10]}")
    
    return protein_cols

def perform_cox_analysis(df, protein_cols, group_name="Overall"):
    """执行COX回归分析"""
    print(f"\n正在进行{group_name}组COX回归分析...")

    results = []
    failed_proteins = []

    # 准备生存数据
    survival_data = df[['OS', 'NonCV_death']].copy()
    survival_data = survival_data.dropna()

    print(f"用于分析的样本数: {len(survival_data)}")
    print(f"事件数 (NonCV_death=1): {survival_data['NonCV_death'].sum()}")

    if survival_data['NonCV_death'].sum() < 5:
        print(f"警告: {group_name}组事件数过少 ({survival_data['NonCV_death'].sum()})，可能影响分析结果")

    # 对每个蛋白进行单因素COX回归
    for i, protein in enumerate(protein_cols):
        if i % 500 == 0:
            print(f"已处理 {i}/{len(protein_cols)} 个蛋白...")

        try:
            # 准备当前蛋白的数据
            protein_data = df[['OS', 'NonCV_death', protein]].copy()
            protein_data = protein_data.dropna()

            if len(protein_data) < 10:  # 样本数太少
                failed_proteins.append((protein, "样本数不足"))
                continue

            if protein_data[protein].std() == 0:  # 无变异
                failed_proteins.append((protein, "无变异"))
                continue

            if protein_data['NonCV_death'].sum() < 2:  # 事件数太少
                failed_proteins.append((protein, "事件数不足"))
                continue

            # 标准化蛋白值
            protein_std = (protein_data[protein] - protein_data[protein].mean()) / protein_data[protein].std()
            protein_data_std = protein_data.copy()
            protein_data_std[protein] = protein_std

            # 执行COX回归
            cph = CoxPHFitter()
            cph.fit(protein_data_std, duration_col='OS', event_col='NonCV_death')

            # 提取结果
            hr = cph.hazard_ratios_[protein]
            ci_lower = cph.confidence_intervals_.loc[protein, '95% lower-bound']
            ci_upper = cph.confidence_intervals_.loc[protein, '95% upper-bound']
            p_value = cph.summary.loc[protein, 'p']

            # 转换置信区间为HR尺度
            ci_lower_hr = np.exp(ci_lower)
            ci_upper_hr = np.exp(ci_upper)

            # 检查结果是否合理
            if np.isnan(hr) or np.isnan(p_value) or np.isinf(hr):
                failed_proteins.append((protein, "结果异常"))
                continue

            results.append({
                'protein': protein,
                'hazard_ratio': hr,
                'ci_lower': ci_lower_hr,
                'ci_upper': ci_upper_hr,
                'p_value': p_value,
                'n_samples': len(protein_data),
                'n_events': int(protein_data['NonCV_death'].sum())
            })

        except Exception as e:
            failed_proteins.append((protein, f"分析错误: {str(e)[:50]}"))
            continue

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    print(f"\n{group_name}组分析结果:")
    print(f"- 尝试分析的蛋白数: {len(protein_cols)}")
    print(f"- 成功分析的蛋白数: {len(results_df)}")
    print(f"- 失败的蛋白数: {len(failed_proteins)}")

    if len(failed_proteins) > 0:
        print(f"- 前5个失败原因: {failed_proteins[:5]}")

    if len(results_df) == 0:
        print(f"警告: {group_name}组没有成功分析的蛋白")
        return pd.DataFrame()

    # 多重检验校正
    _, fdr_values, _, _ = multipletests(results_df['p_value'], method='fdr_bh')
    results_df['fdr_value'] = fdr_values

    # 按p值排序
    results_df = results_df.sort_values('p_value').reset_index(drop=True)

    print(f"- P < 0.05的蛋白数: {(results_df['p_value'] < 0.05).sum()}")
    print(f"- FDR < 0.05的蛋白数: {(results_df['fdr_value'] < 0.05).sum()}")

    return results_df

def create_volcano_plot(data, title, filename, group_name="Overall"):
    """创建火山图"""
    if len(data) == 0:
        print(f"警告: {group_name}组没有数据可绘制火山图")
        return
        
    print(f"正在创建{title}...")
    
    # 准备数据
    plot_data = data.copy()
    plot_data['log2_hr'] = np.log2(plot_data['hazard_ratio'])
    plot_data['neg_log10_p'] = -np.log10(plot_data['p_value'])
    
    # 定义阈值
    p_threshold = 0.05
    fdr_threshold = 0.05
    
    # 分类点
    plot_data['significance'] = 'Non-significant'
    
    # P值显著
    p_significant = plot_data['p_value'] < p_threshold
    fdr_significant = plot_data['fdr_value'] < fdr_threshold
    
    # 分类：保护性、危险性、非显著
    sig_protective = p_significant & (plot_data['hazard_ratio'] < 1)
    sig_risk = p_significant & (plot_data['hazard_ratio'] > 1)
    
    plot_data.loc[sig_protective, 'significance'] = 'Significant_Protective'
    plot_data.loc[sig_risk, 'significance'] = 'Significant_Risk'
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 10))
    ax.set_facecolor('white')
    
    # 定义颜色和大小
    colors = {
        'Non-significant': '#CCCCCC',
        'Significant_Protective': '#1E88E5',
        'Significant_Risk': '#D32F2F'
    }
    
    sizes = {
        'Non-significant': 35,
        'Significant_Protective': 55,
        'Significant_Risk': 55
    }
    
    # 绘制散点图
    for category in ['Non-significant', 'Significant_Protective', 'Significant_Risk']:
        mask = plot_data['significance'] == category
        if mask.sum() > 0:
            alpha = 0.6 if category == 'Non-significant' else 0.8
            zorder = 1 if category == 'Non-significant' else 3
            
            ax.scatter(plot_data.loc[mask, 'log2_hr'],
                      plot_data.loc[mask, 'neg_log10_p'],
                      c=colors[category],
                      alpha=alpha,
                      s=sizes[category],
                      zorder=zorder,
                      edgecolors='white' if category != 'Non-significant' else 'none',
                      linewidth=0.5)
    
    # 添加阈值线
    ax.axhline(y=-np.log10(p_threshold), color='black', linestyle='--',
               alpha=0.8, linewidth=1.5, zorder=2)
    ax.axvline(x=0, color='black', linestyle='-', alpha=0.6, linewidth=1, zorder=2)
    
    # 设置坐标轴范围
    x_range = max(abs(plot_data['log2_hr'].min()), abs(plot_data['log2_hr'].max()))
    ax.set_xlim(-x_range*1.1, x_range*1.1)
    
    y_max = plot_data['neg_log10_p'].max() * 1.05
    ax.set_ylim(0, max(y_max, 3))
    
    # 设置标签和标题
    ax.set_xlabel('log₂(Hazard Ratio)', fontsize=12, fontweight='bold')
    ax.set_ylabel('-log₁₀(P value)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=15)
    
    # 添加网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    
    # 添加标注
    ax.text(-x_range*1.05, -np.log10(p_threshold) + 0.1, 'P = 0.05',
            fontsize=10, color='black', fontweight='bold')
    
    # 添加保护性/危险性标注
    ax.text(-x_range*1.05, 0.2, 'Protective', fontsize=11, color='#1E88E5', 
            fontweight='bold', ha='left')
    ax.text(x_range*1.05, 0.2, 'Risk', fontsize=11, color='#D32F2F', 
            fontweight='bold', ha='right')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(1.5)
    ax.spines['bottom'].set_linewidth(1.5)
    
    # 添加统计信息
    stats_text = f"Total: {len(plot_data)}\n"
    stats_text += f"P < 0.05: {p_significant.sum()}\n"
    stats_text += f"FDR < 0.05: {fdr_significant.sum()}"
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
            verticalalignment='top', fontsize=9,
            bbox=dict(boxstyle='round,pad=0.4', facecolor='white', 
                     alpha=0.9, edgecolor='gray'))
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_forest_plot(data, title, filename, top_n=20):
    """创建森林图"""
    if len(data) == 0:
        print(f"警告: 没有数据可绘制森林图")
        return
        
    print(f"正在创建{title}...")
    
    # 选择前N个最显著的结果
    plot_data = data.head(top_n).copy()
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(14, max(8, len(plot_data) * 0.8)))
    
    # 绘制森林图
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i  # 倒序排列，最显著的在顶部
        
        # 绘制置信区间线
        ax.plot([row['ci_lower'], row['ci_upper']], [y, y], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制置信区间端点
        ax.plot([row['ci_lower'], row['ci_lower']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        ax.plot([row['ci_upper'], row['ci_upper']], [y-0.1, y+0.1], 
                color='black', linewidth=2, alpha=0.8, zorder=2)
        
        # 绘制HR点
        color = '#1E88E5' if row['hazard_ratio'] < 1 else '#D32F2F'
        ax.scatter(row['hazard_ratio'], y, s=150, color=color, alpha=0.8, 
                  zorder=3, marker='s', edgecolors='black', linewidth=1)
    
    # 添加HR=1的参考线
    ax.axvline(x=1, color='black', linestyle='--', alpha=0.8, linewidth=2, zorder=1)
    
    # 设置y轴标签
    protein_names = []
    for _, row in plot_data.iterrows():
        name = row['protein']
        if len(name) > 45:
            name = name[:42] + '...'
        protein_names.append(name)
    
    ax.set_yticks(range(len(plot_data)))
    ax.set_yticklabels(reversed(protein_names), fontsize=10)
    
    # 设置x轴
    ax.set_xscale('log')
    
    # 设置x轴范围
    all_values = list(plot_data['hazard_ratio']) + list(plot_data['ci_lower']) + list(plot_data['ci_upper'])
    x_min = max(0.01, min(all_values) * 0.5)
    x_max = max(all_values) * 2
    ax.set_xlim(x_min, x_max)
    
    # 设置标签
    ax.set_xlabel('Hazard Ratio (95% CI)', fontsize=12, fontweight='bold')
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    
    # 添加网格
    ax.grid(True, alpha=0.3, axis='x')
    
    # 在右侧添加HR值、置信区间和P值
    for i, (idx, row) in enumerate(plot_data.iterrows()):
        y = len(plot_data) - 1 - i
        
        hr_text = f"{row['hazard_ratio']:.3f}"
        ci_text = f"[{row['ci_lower']:.3f}-{row['ci_upper']:.3f}]"
        p_text = f"P={row['p_value']:.4f}"
        fdr_text = f"FDR={row['fdr_value']:.4f}"
        
        ax.text(x_max * 0.95, y + 0.1, f"{hr_text} {ci_text}", 
                ha='right', va='center', fontsize=9, fontweight='bold')
        ax.text(x_max * 0.95, y - 0.1, f"{p_text}, {fdr_text}", 
                ha='right', va='center', fontsize=8, style='italic', color='gray')
    
    # 美化坐标轴
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['bottom'].set_linewidth(1.5)
    ax.spines['left'].set_linewidth(1.5)
    
    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()
    
    print(f"{title}已保存到: {filename}")

def create_venn_diagram(hfnef_results, hfsnef_results, filename):
    """创建简化的韦恩图显示两组差异蛋白的重叠"""
    print("正在创建韦恩图...")

    # 获取显著的蛋白
    hfnef_sig = set(hfnef_results[hfnef_results['p_value'] < 0.05]['protein'])
    hfsnef_sig = set(hfsnef_results[hfsnef_results['p_value'] < 0.05]['protein'])

    # 计算重叠
    common = hfnef_sig & hfsnef_sig
    hfnef_only = hfnef_sig - hfsnef_sig
    hfsnef_only = hfsnef_sig - hfnef_sig

    # 创建简化的韦恩图
    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制两个圆
    circle1 = plt.Circle((0.35, 0.5), 0.3, color='#1E88E5', alpha=0.5, label='HFnEF')
    circle2 = plt.Circle((0.65, 0.5), 0.3, color='#D32F2F', alpha=0.5, label='HFsnEF')

    ax.add_patch(circle1)
    ax.add_patch(circle2)

    # 添加数字标注
    ax.text(0.25, 0.5, str(len(hfnef_only)), fontsize=16, ha='center', va='center', fontweight='bold')
    ax.text(0.5, 0.5, str(len(common)), fontsize=16, ha='center', va='center', fontweight='bold')
    ax.text(0.75, 0.5, str(len(hfsnef_only)), fontsize=16, ha='center', va='center', fontweight='bold')

    # 添加标签
    ax.text(0.25, 0.15, 'HFnEF Only', fontsize=12, ha='center', fontweight='bold', color='#1E88E5')
    ax.text(0.5, 0.15, 'Common', fontsize=12, ha='center', fontweight='bold', color='#4CAF50')
    ax.text(0.75, 0.15, 'HFsnEF Only', fontsize=12, ha='center', fontweight='bold', color='#D32F2F')

    # 设置坐标轴
    ax.set_xlim(0, 1)
    ax.set_ylim(0, 1)
    ax.set_aspect('equal')
    ax.axis('off')

    # 设置标题
    ax.set_title('Significant Proteins (P < 0.05) - HFnEF vs HFsnEF Groups',
                fontsize=14, fontweight='bold', pad=20)

    plt.tight_layout()
    plt.savefig(filename, dpi=300, bbox_inches='tight', facecolor='white')
    plt.show()

    print(f"韦恩图已保存到: {filename}")

    # 打印统计信息
    print(f"HFnEF组显著蛋白数: {len(hfnef_sig)}")
    print(f"HFsnEF组显著蛋白数: {len(hfsnef_sig)}")
    print(f"共同显著蛋白数: {len(common)}")
    print(f"HFnEF组特异蛋白数: {len(hfnef_only)}")
    print(f"HFsnEF组特异蛋白数: {len(hfsnef_only)}")

    return hfnef_sig, hfsnef_sig

def main():
    """主函数"""
    print("=" * 80)
    print("蛋白组COX回归分析 - 非心血管死亡相关分析")
    print("=" * 80)

    # 1. 加载和准备数据
    df = load_and_prepare_data()
    protein_cols = get_protein_columns(df)

    # 2. 整体人群分析
    print("\n" + "="*50)
    print("1. 整体人群COX回归分析")
    print("="*50)

    overall_results = perform_cox_analysis(df, protein_cols, "Overall")

    if len(overall_results) > 0:
        # 保存结果
        overall_results.to_csv('overall_protein_cox_results.csv', index=False)
        print("整体人群结果已保存到: overall_protein_cox_results.csv")

        # 绘制火山图
        create_volcano_plot(overall_results,
                           'Overall Population - Protein Cox Regression (NonCV Death)',
                           'overall_protein_volcano_plot.png',
                           'Overall')

        # 绘制森林图 - 前20个最显著的蛋白
        create_forest_plot(overall_results,
                          'Top 20 Most Significant Proteins - Overall Population (NonCV Death)',
                          'overall_protein_forest_plot_top20.png', 20)

    # 3. HFnEF组分析
    print("\n" + "="*50)
    print("2. HFnEF组COX回归分析")
    print("="*50)

    df_hfnef = df[df['HFsnEF'] == 0].copy()
    print(f"HFnEF组样本数: {len(df_hfnef)}")

    hfnef_results = perform_cox_analysis(df_hfnef, protein_cols, "HFnEF")

    if len(hfnef_results) > 0:
        # 保存结果
        hfnef_results.to_csv('hfnef_protein_cox_results.csv', index=False)
        print("HFnEF组结果已保存到: hfnef_protein_cox_results.csv")

        # 绘制火山图
        create_volcano_plot(hfnef_results,
                           'HFnEF Group - Protein Cox Regression (NonCV Death)',
                           'hfnef_protein_volcano_plot.png',
                           'HFnEF')

    # 4. HFsnEF组分析
    print("\n" + "="*50)
    print("3. HFsnEF组COX回归分析")
    print("="*50)

    df_hfsnef = df[df['HFsnEF'] == 1].copy()
    print(f"HFsnEF组样本数: {len(df_hfsnef)}")

    hfsnef_results = perform_cox_analysis(df_hfsnef, protein_cols, "HFsnEF")

    if len(hfsnef_results) > 0:
        # 保存结果
        hfsnef_results.to_csv('hfsnef_protein_cox_results.csv', index=False)
        print("HFsnEF组结果已保存到: hfsnef_protein_cox_results.csv")

        # 绘制火山图
        create_volcano_plot(hfsnef_results,
                           'HFsnEF Group - Protein Cox Regression (NonCV Death)',
                           'hfsnef_protein_volcano_plot.png',
                           'HFsnEF')

    # 5. 韦恩图和HFsnEF特异蛋白森林图
    if len(hfnef_results) > 0 and len(hfsnef_results) > 0:
        print("\n" + "="*50)
        print("4. 亚组比较分析")
        print("="*50)

        # 创建韦恩图
        hfnef_sig, hfsnef_sig = create_venn_diagram(hfnef_results, hfsnef_results,
                                                   'protein_venn_diagram.png')

        # HFsnEF组特异蛋白森林图
        hfsnef_specific = hfsnef_sig - hfnef_sig
        if len(hfsnef_specific) > 0:
            hfsnef_specific_results = hfsnef_results[
                hfsnef_results['protein'].isin(hfsnef_specific)
            ].sort_values('p_value')

            create_forest_plot(hfsnef_specific_results,
                              'HFsnEF-Specific Significant Proteins (NonCV Death)',
                              'hfsnef_specific_protein_forest_plot.png',
                              min(len(hfsnef_specific_results), 15))

    print(f"\n" + "="*80)
    print("分析完成！")
    print("生成的文件:")
    print("• overall_protein_cox_results.csv - 整体人群COX回归结果")
    print("• overall_protein_volcano_plot.png - 整体人群火山图")
    print("• overall_protein_forest_plot_top20.png - 整体人群前20个蛋白森林图")
    print("• hfnef_protein_cox_results.csv - HFnEF组COX回归结果")
    print("• hfnef_protein_volcano_plot.png - HFnEF组火山图")
    print("• hfsnef_protein_cox_results.csv - HFsnEF组COX回归结果")
    print("• hfsnef_protein_volcano_plot.png - HFsnEF组火山图")
    print("• protein_venn_diagram.png - 两组差异蛋白韦恩图")
    print("• hfsnef_specific_protein_forest_plot.png - HFsnEF组特异蛋白森林图")
    print("="*80)

if __name__ == "__main__":
    main()
