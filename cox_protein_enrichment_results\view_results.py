#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查看Cox蛋白功能富集分析结果
"""

import pandas as pd
import json
from pathlib import Path
import matplotlib.pyplot as plt
from PIL import Image
import matplotlib.image as mpimg

def display_results():
    """显示分析结果"""
    results_dir = Path(".")
    
    print("="*80)
    print("Cox蛋白功能富集分析结果展示")
    print("="*80)
    
    # 1. 显示KEGG富集结果
    print("\n1. KEGG通路富集分析结果（前10个）:")
    print("-"*60)
    kegg_results = pd.read_csv(results_dir / "enrichment_results" / "kegg_enrichment_results.csv")
    kegg_top10 = kegg_results.head(10)
    
    for idx, row in kegg_top10.iterrows():
        print(f"{idx+1:2d}. {row['term']:<35} | 蛋白数: {row['count']:2d} | 比例: {row['ratio']:.3f} | p值: {row['p_value']:.4f}")
    
    # 2. 显示GO-BP富集结果
    print("\n2. GO生物过程富集分析结果（前10个）:")
    print("-"*60)
    go_results = pd.read_csv(results_dir / "enrichment_results" / "go_bp_enrichment_results.csv")
    go_top10 = go_results.head(10)
    
    for idx, row in go_top10.iterrows():
        print(f"{idx+1:2d}. {row['term']:<35} | 蛋白数: {row['count']:2d} | 比例: {row['ratio']:.3f} | p值: {row['p_value']:.4f}")
    
    # 3. 显示注释统计
    print("\n3. 功能注释统计:")
    print("-"*60)
    
    # KEGG注释
    with open(results_dir / "annotations" / "kegg_annotations.json", 'r', encoding='utf-8') as f:
        kegg_annotations = json.load(f)
    
    # GO-BP注释
    with open(results_dir / "annotations" / "go_bp_annotations.json", 'r', encoding='utf-8') as f:
        go_annotations = json.load(f)
    
    print(f"KEGG注释蛋白数: {len(kegg_annotations)}")
    print(f"GO-BP注释蛋白数: {len(go_annotations)}")
    
    # 统计通路/过程数量
    all_kegg_terms = []
    for terms in kegg_annotations.values():
        all_kegg_terms.extend(terms)
    
    all_go_terms = []
    for terms in go_annotations.values():
        all_go_terms.extend(terms)
    
    print(f"涉及KEGG通路数: {len(set(all_kegg_terms))}")
    print(f"涉及GO生物过程数: {len(set(all_go_terms))}")
    
    # 4. 文件列表
    print("\n4. 生成的文件:")
    print("-"*60)
    print("📁 annotations/")
    print("   ├── kegg_annotations.json      - KEGG通路注释结果")
    print("   └── go_bp_annotations.json     - GO生物过程注释结果")
    print("📁 enrichment_results/")
    print("   ├── kegg_enrichment_results.csv - KEGG富集分析结果")
    print("   └── go_bp_enrichment_results.csv - GO-BP富集分析结果")
    print("📁 bubble_plots/")
    print("   ├── kegg_enrichment_bubble.png  - KEGG富集气泡图")
    print("   └── go_bp_enrichment_bubble.png - GO-BP富集气泡图")
    print("📁 reports/")
    print("   ├── analysis_report.md          - 基础分析报告")
    print("   └── detailed_analysis_report.md - 详细分析报告")
    
    print("\n5. 主要发现:")
    print("-"*60)
    print("🔍 KEGG通路分析:")
    print(f"   • 最显著富集通路: {kegg_results.iloc[0]['term']}")
    print(f"   • 富集蛋白数: {kegg_results.iloc[0]['count']} 个")
    print(f"   • 富集比例: {kegg_results.iloc[0]['ratio']:.1%}")
    
    print("🔍 GO生物过程分析:")
    print(f"   • 最显著富集过程: {go_results.iloc[0]['term']}")
    print(f"   • 富集蛋白数: {go_results.iloc[0]['count']} 个")
    print(f"   • 富集比例: {go_results.iloc[0]['ratio']:.1%}")
    
    print("\n6. 生物学意义:")
    print("-"*60)
    print("💡 关键发现:")
    print("   • 胰岛素信号通路高度富集，提示代谢调节的重要性")
    print("   • 凋亡通路显著富集，与细胞生存调控相关")
    print("   • 血管生成过程富集度最高，可能与疾病进展相关")
    print("   • 自噬过程显著富集，涉及细胞稳态维持")
    
    print("\n" + "="*80)
    print("分析完成！请查看bubble_plots文件夹中的气泡图可视化结果")
    print("详细报告请参阅reports/detailed_analysis_report.md")
    print("="*80)

def show_bubble_plots():
    """显示气泡图"""
    try:
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # KEGG气泡图
        kegg_img = mpimg.imread("bubble_plots/kegg_enrichment_bubble.png")
        ax1.imshow(kegg_img)
        ax1.set_title("KEGG通路富集分析气泡图", fontsize=14, fontweight='bold')
        ax1.axis('off')
        
        # GO-BP气泡图
        go_img = mpimg.imread("bubble_plots/go_bp_enrichment_bubble.png")
        ax2.imshow(go_img)
        ax2.set_title("GO生物过程富集分析气泡图", fontsize=14, fontweight='bold')
        ax2.axis('off')
        
        plt.tight_layout()
        plt.savefig("combined_bubble_plots.png", dpi=300, bbox_inches='tight')
        plt.show()
        
        print("气泡图已显示并保存为 combined_bubble_plots.png")
        
    except Exception as e:
        print(f"显示气泡图时出错: {e}")
        print("请直接查看bubble_plots文件夹中的PNG文件")

if __name__ == "__main__":
    display_results()
    
    # 询问是否显示气泡图
    try:
        show_plots = input("\n是否显示气泡图? (y/n): ").lower().strip()
        if show_plots in ['y', 'yes', '是']:
            show_bubble_plots()
    except:
        print("跳过气泡图显示")
